<script setup>
import {computed, onMounted, onUnmounted, ref, watch} from 'vue';
import {eachMonthOfInterval, format, nextSunday} from 'date-fns';
import {useRoute, useRouter} from 'vue-router';
import {useMagicKeys} from '@vueuse/core';
import {nl} from "date-fns/locale";
import filters from '@/helpers/filters'
import {debounceMseconds} from "@/helpers/constants.js";

const router = useRouter();
const route = useRoute();
const keys = useMagicKeys();
const keyPrev = keys['Ctrl+Left'];
const keyNext = keys['Ctrl+Right'];
const month = ref();
const debounce = ref();

onMounted(() => {
  month.value = {
    year: route.params.year,
    month: route.params.month,
  }
});

onUnmounted(() => {
  clearTimeout(debounce.value);
});

const months = ref(
  eachMonthOfInterval({
    start: nextSunday(new Date()),
    end: new Date(2024, 0, 4)

  }).map(date => ({
    title: filters.ucFirst(format(date, 'MMMM yyyy', { locale: nl })),
    value: {
      year: format(date, 'yyyy'),
      month: format(date, 'M') // Month number
    },
  }))
);

const getCurrentMonthIndex = () => {
  return months.value.findIndex(m => m.value.year === month.value.year && m.value.month === month.value.month);
}

const selectPrevMonth = () => {

  let currentIndex = getCurrentMonthIndex();

  if (currentIndex < months.value.length - 1) {
    month.value = months.value[currentIndex + 1].value;
  }
};

const selectNextMonth = () => {
  let currentIndex = getCurrentMonthIndex();
  if (currentIndex < months.value.length) {
    month.value = months.value[currentIndex - 1].value;
  }
};

const selectToday = () => {
  let date = new Date();
  month.value = {
    year: format(date, 'yyyy'),
    month: format(date, 'M'),
  }
};

const isCurrentMonth = computed(() => {
  const today = new Date();
  return (
    month.value.year === format(today, 'yyyy') &&
    month.value.month === format(today, 'M')
  );
});


// Watch for keyboard shortcuts to navigate between months
watch(keyPrev, (pressed) => {
  if (pressed) {
    selectPrevMonth();
  }
});

watch(keyNext, (pressed) => {
  if (pressed) {
    selectNextMonth();
  }
});

watch(month, (val) => {
  clearTimeout(debounce.value);
  debounce.value = setTimeout(() => {
    router.replace({
      name: 'accredis-details', params: {
        user: route.params.user,
        month: val.month,
        year: val.year,
      }
    });
  }, debounceMseconds);
});

watch(route, (to) => {
  month.value = {
    month: to.params.month,
    year: to.params.year,
  };
}, {flush: 'pre', immediate: true, deep: true});

</script>

<template>
  <v-btn icon @click="selectPrevMonth">
    <v-icon>mdi-chevron-left</v-icon>
  </v-btn>
  <v-select
    v-model="month"
    :items="months"
    density="compact"
    class="month-select mt-5"
    variant="outlined"
  />

  <v-btn icon @click="selectToday" title="Go to current month">
    <v-icon>mdi-calendar-today</v-icon>
  </v-btn>

  <v-btn icon @click="selectNextMonth" :disabled="isCurrentMonth">
    <v-icon>mdi-chevron-right</v-icon>
  </v-btn>
</template>

<style>
.month-select {
  max-width: 185px;
}
</style>
