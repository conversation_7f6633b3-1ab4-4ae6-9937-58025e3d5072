<?php

  use domain\google\service\GoogleCalendarService;
  use Google\Service\Calendar\Events;
  use Google\Service\Exception;

  AppModel::loadModelClass('IndufastEmployeeModel');

  class IndufastEmployee extends IndufastEmployeeModel {

    use ModelFillTrait;
    use ValidationTrait;
    use PropertyCastTrait {
      castProperties as castPropertiesTrait;
    }

    private bool $availabilityError = false;

    const MORNING_START = '08:00:00';
    const MORNING_END = '12:00:00';
    const AFTERNOON_START = '13:00:00';
    const AFTERNOON_END = '17:00:00';
    const AVAILABLE = 'available';
    const AVAILABLE_MORNING = 'available_morning';
    const AVAILABLE_AFTERNOON = 'available_afternoon';
    const AVAILABLE_MORNING_AFTERNOON = 'available_morning_afternoon';
    const NOT_AVAILABLE = 'not_available';
    const NOT_AVAILABLE_ERROR = 'not_available_error';
    const string NOT_AVAILABLE_NON_WORKING_DAY = 'not_available_non_working_day';

    const array CAST_PROPERTIES = [
      'id'                            => 'int',
      'rank_number'                   => 'int',
      'private'                       => 'boolean',
      'industries'                    => 'array',
      'non_working_days_even_weeks'   => 'array',
      'non_working_days_uneven_weeks' => 'array',
      'team_lead'                     => 'boolean',
      'active'                        => 'boolean',
      'monthly_percentage'            => 'int',
      'may_login'                     => 'boolean',
      'from_db'                       => 'hidden',
      'insertTS'                      => 'hidden',
      'updateTS'                      => 'hidden',
    ];

    const INDUSTRIES = [
      'agriculture',
      'industries',
      'automotive',
      'car_wash',
    ];

    const array NON_WORKING_DAYS = [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
      'sunday',
    ];

    protected array $fillable = [
      'name'                          => 'string|required|max:255',
      'name_accredis'                 => 'string|nullable|max:255',
      'type'                          => 'string|required|in:staff,subcontractor',
      'email'                         => 'required|email|max:255|unique:indufast_employee,email,id,{id}|unique2:indufast_employee,extra_email,id,{id}',
      'extra_email'                   => 'nullable|email|max:255|unique:indufast_employee,email,id,{id}|unique2:indufast_employee,extra_email,id,{id}',
      'rank'                          => 'string|in:A,B,C,D,E|required_if:active,1',
      'rank_number'                   => 'integer|min:1|max:10|required_if:active,1',
      'industries'                    => 'array|nullable',
      'non_working_days_even_weeks'   => 'array|nullable',
      'non_working_days_uneven_weeks' => 'array|nullable',
      'private'                       => 'boolean|required',
      'team_lead'                     => 'boolean|required',
      'active'                        => 'boolean|required',
      'monthly_percentage'            => 'integer|min:1|max:100|required_if:active,1',
      'may_login'                     => 'boolean|required',
      'usergroup'                     => 'string|required_if:may_login,1|max:20',
    ];

    public function getFillable(): array {
      $this->fillable['industries.*'] = 'string|in:' . implode(',', self::INDUSTRIES);
      $this->fillable['non_working_days_even_weeks.*'] = 'string|in:' . implode(',', self::NON_WORKING_DAYS);
      $this->fillable['non_working_days_uneven_weeks.*'] = 'string|in:' . implode(',', self::NON_WORKING_DAYS);
      return $this->fillable;
    }

    public function castProperties(): void {
      $this->castPropertiesTrait();
      if ($this->industries) {
        usort($this->industries, function ($a, $b) {
          $order = array_flip(self::INDUSTRIES);
          return $order[$a] <=> $order[$b];
        });
      }
      if ($this->non_working_days_even_weeks) {
        usort($this->non_working_days_even_weeks, function ($a, $b) {
          $order = array_flip(self::NON_WORKING_DAYS);
          return $order[$a] <=> $order[$b];
        });
      }
      if ($this->non_working_days_uneven_weeks) {
        usort($this->non_working_days_uneven_weeks, function ($a, $b) {
          $order = array_flip(self::NON_WORKING_DAYS);
          return $order[$a] <=> $order[$b];
        });
      }
    }

    public function save(array &$errors = []): mysqli_result|bool {
      $this->castPropertiesForSave();
      return parent::save($errors);
    }

    public function getAvailability(string $date, bool $ignoreIndufastEvents = false, ?IndufastCalendarEvent $eventToIgnore = null): string {
      $events = $this->getCalendarEvents($date);
      return $this->calculateAvailabilityFromEvents($events, $date, $ignoreIndufastEvents, $eventToIgnore);
    }

    public function isNonWorkingDay(string $date): bool {
      $today = new DateTime($date);
      $weekNumber = (int)$today->format('W');
      $isEvenWeek = $weekNumber % 2 === 0;

      return in_array(
        strtolower($today->format('l')),
        ($isEvenWeek) ? json_decode($this->non_working_days_even_weeks ?? '[]') : json_decode($this->non_working_days_uneven_weeks ?? '[]')
      );
    }

    public function calculateAvailabilityFromEvents(array $calendarEvents, string $date, bool $ignoreIndufastEvents = false, ?IndufastCalendarEvent $eventToIgnore = null): string {
      if ($this->availabilityError) {
        return self::NOT_AVAILABLE_ERROR;
      }
      if ($this->isNonWorkingDay($date)) {
        return self::NOT_AVAILABLE_NON_WORKING_DAY;
      }

      $available_morning = $available_afternoon = $available_lunch = true;

      if ($ignoreIndufastEvents) {
        $calendarEventIds = array_column($calendarEvents, 'id');
        $query = "SELECT id, google_calendar_event_id FROM indufast_calendar_event WHERE" . DbHelper::getSqlIn('google_calendar_event_id', $calendarEventIds);
        $result = DBConn::db_link()->query($query);
        $indufastEvents = $result->fetch_all(MYSQLI_ASSOC);
      }

      foreach ($calendarEvents as $event) {
        if ($eventToIgnore && $event->id === $eventToIgnore->google_calendar_event_id) {
          continue;
        }
        if ($ignoreIndufastEvents && in_array($event->id, array_column($indufastEvents ?? [], 'google_calendar_event_id'))) {
          continue;
        }
        if ($event->getStart()->getDate()) {
          return self::NOT_AVAILABLE;
        }
        if ($event->responseStatus === 'declined') {
          continue;
        }

        $eventStart = new DateTime($event->getStart()->getDateTime());
        $eventEnd = new DateTime($event->getEnd()->getDateTime());
        $morningStart = new DateTime($date . ' ' . self::MORNING_START);
        $morningEnd = new DateTime($date . ' ' . self::MORNING_END);
        $afternoonStart = new DateTime($date . ' ' . self::AFTERNOON_START);
        $afternoonEnd = new DateTime($date . ' ' . self::AFTERNOON_END);

        if ($eventStart < $morningEnd && $eventEnd > $morningStart) {
          $available_morning = false;
        }
        if ($eventStart < $afternoonStart && $eventEnd > $morningEnd) {
          $available_lunch = false;
        }
        if ($eventStart < $afternoonEnd && $eventEnd > $afternoonStart) {
          $available_afternoon = false;
        }
      }

      if ($available_morning && $available_afternoon && $available_lunch) {
        return self::AVAILABLE;
      }
      if ($available_morning && $available_afternoon) {
        return self::AVAILABLE_MORNING_AFTERNOON;
      }
      if ($available_morning) {
        return self::AVAILABLE_MORNING;
      }
      if ($available_afternoon) {
        return self::AVAILABLE_AFTERNOON;
      }
      return self::NOT_AVAILABLE;
    }

    public function hasConflict(IndufastCalendarEvent $event, string $availability): bool {
      return match ($availability) {
        IndufastEmployee::AVAILABLE_MORNING => $event->day_part !== \IndufastCalendarEvent::DAY_PART_MORNING,
        IndufastEmployee::AVAILABLE_AFTERNOON => $event->day_part !== \IndufastCalendarEvent::DAY_PART_AFTERNOON,
        IndufastEmployee::AVAILABLE_MORNING_AFTERNOON => !in_array($event->day_part, [\IndufastCalendarEvent::DAY_PART_MORNING, \IndufastCalendarEvent::DAY_PART_AFTERNOON]),
        IndufastEmployee::AVAILABLE,
        IndufastEmployee::NOT_AVAILABLE_ERROR ,
        IndufastEmployee::NOT_AVAILABLE_NON_WORKING_DAY => false,
        default => true,
      };
    }

    public function getCalendarEvents(string $date): array {
      $eventsForEmployees = self::getCalendarEventsForEmployees([$this], $date);
      return $eventsForEmployees[$this->id];
    }

    /**
     * Get calendar events for multiple employees.
     *
     * @param IndufastEmployee[] $employees
     * @param string $startDate
     * @param string|null $endDate
     *
     * @return array
     */
    public static function getCalendarEventsForEmployees(array $employees, string $startDate, ?string $endDate = null): array {
        $service = new GoogleCalendarService();
        $calendarIds = array_map(fn($employee) => $employee->email, $employees);
        
        $eventsByCalendar = $service->getGoogleCalendarEventsBatch($calendarIds, $startDate, $endDate);
        $eventsByEmployee = [];

        foreach ($employees as $employee) {
          $events = $eventsByCalendar[$employee->email] ?? null;

          if ($events instanceof Events) {
            $items = $events->getItems();
          } elseif ($events instanceof Exception) {
            $employee->availabilityError = true;
            $items = [];
          } else {
            $items = [];
          }
          self::setResponseStatus($items, $employee->email);

          $eventsByEmployee[$employee->id] = $items;
        }
        return $eventsByEmployee;
    }

    /**
     * Get the response status of an attendee from an event.
     *
     * @param array $events
     * @param string $employeeEmail
     */
    public static function setResponseStatus(array $events, string $employeeEmail): void {
      foreach ($events as $event) {
        $attendee = array_find($event->getAttendees(), fn ($attendee) => $attendee->getEmail() === $employeeEmail);
        $event->responseStatus = $attendee ? $attendee->getResponseStatus() : null;
      }
    }
  }
