<script setup xmlns="http://www.w3.org/1999/html">
import {computed, onMounted, ref, watch} from "vue";
import {useRouter, useRoute} from "vue-router";
import {useTitle} from "vue-page-title";
import createApiService from "@/services/api.js";
import {definePage} from "unplugin-vue-router/runtime";
import {eachDayOfInterval, format, getWeek} from "date-fns";
import { vMaska } from "maska/vue"
import {holidayDisplayNames} from "@/helpers/constants.js";
import WorkdayEditDialog from "@/components/accredis/WorkdayEditDialog.vue";
import {timeInMinutes} from "@/helpers/helpers.js";

const router = useRouter();
const api = createApiService(router);
const {setTitle} = useTitle();
const route = useRoute();
const summary = ref();
const workdays = ref();
const changedWorkdays = ref({});
const loading = ref({});
const user = ref();
const openDays = ref([]);
const newLine = ref({
  begin: '',
  end: '',
});
const holidays = ref();

definePage({
  name: "accredis-details",
  meta: {
    title: 'Accredis',
    requiresAuth: true,
  },
})

onMounted(() => {
  refreshWorkdays();
});

const refreshWorkdays = async () => {
  changedWorkdays.value = {}

  try {
    const workdayParams = new URLSearchParams({
      employee_id: route.params.employee,
      year: route.params.year,
      month: route.params.month,
    }).toString();

    const [holidaysResponse, employeeResponse, workdaysResponse] = await Promise.all([
      holidays.value ?? api.get("getHolidays"),
      user.value ?? api.get(`employeeGet?id=${route.params.employee}`),
      api.get(`workdayList?${workdayParams}`)
    ]);

    holidays.value ??= holidaysResponse.data.data;
    user.value ??= employeeResponse.data.data;
    setTitle(user.value.name);

    workdays.value = [];
    openDays.value = [];
    eachDayOfInterval({
      start: new Date(route.params.year, route.params.month - 1, 1),
      end: new Date(route.params.year, route.params.month, 0),
    }).forEach((date) => {
      const workday = getWorkday(date, workdaysResponse.data.data);
      workdays.value.push(workday);
      if (workday.status === 'new' && workday.id) {
        openDays.value.push(workdays.value.length - 1);
      }
    });

    refreshWorkdaySummary();
  } catch (error) {
    console.log(error);
  }
}

const getWorkday = (date, workdays) => {
  const workday = workdays.find((workday) => workday.date === format(date, 'yyyy-MM-dd'));
  if (workday) {
    return workday;
  }
  else {
    return {
      id: null,
      date: format(date, 'yyyy-MM-dd'),
      status: 'new',
      lines: [],
      travel_to_end_line_id: null,
      travel_from_start_line_id: null,
      remark: '',
    };
  }
}

const refreshWorkdaySummary = () => {
  api.get("workdaySummary?" + new URLSearchParams({
    employee_id: route.params.employee,
    year: route.params.year,
    month: route.params.month,
  }).toString())
    .then((response) => {
      summary.value = response.data.data;
    })
    .catch((error) => {
      console.log(error);
    });
}

const updateCalculation = async (workday) => {
  let data = JSON.parse(JSON.stringify(workday));
  data.workdayLinesData = {};
  data.lines.forEach((line) => {
    data.workdayLinesData[line.id] = {
      void: line.void,
    };
  });

  loading.value[workday.date] = true;

  await api
    .post("workdayCalculate?id=" + workday.id, JSON.stringify(data))
    .then((response) => {
      const workdayIndex = workdays.value.findIndex((workdayLocal) => workdayLocal.id === workday.id);
      workdays.value[workdayIndex] = response.data.data;
    })
    .catch((error) => {
      console.log(error);
    });

  loading.value[workday.date] = false;
}

const saveWorkday = (workday) => {
  loading.value[workday.date] = true;
  let workdayLinesData = {};
  workday.lines.forEach((line) => {
    workdayLinesData[line.id] = {
      void: line.void,
    };
  });

  let json = JSON.stringify({
    employee_id: route.params.employee,
    date: workday.date,
    travel_to_end_line_id: workday.travel_to_end_line_id,
    travel_from_start_line_id: workday.travel_from_start_line_id,
    workdayLinesData: workdayLinesData,
    remark: workday.remark,
    status: workday.status,
    break_time: workday.break_time,
    work_time: workday.work_time,
  });
  api
    .post((workday.id) ? "workdayUpdate?id=" + workday.id : "workdayCreate", json)
    .then((response) => {
      const workdayIndex = workdays.value.findIndex((workdayLocal) => workdayLocal.date === workday.date);
      workdays.value[workdayIndex] = response.data.data;
      delete changedWorkdays.value[workday.date];
      if (workdays.value[workdayIndex].status === 'processed') {
        openDays.value = openDays.value.filter((day) => day !== workdayIndex);
      }
      refreshWorkdaySummary()
      loading.value[workday.date] = false;
    })
    .catch((error) => {
      loading.value[workday.date] = false;
      console.log(error);
    });
}

const updateWorkday = (workday, newWorkday) => {
  const workdayIndex = workdays.value.findIndex((workdayLocal) => workdayLocal.id === workday.id);

  // Preserve some data of the original workday.
  newWorkday.status = workday.status;
  newWorkday.remark = workday.remark;

  workdays.value[workdayIndex] = newWorkday;
  delete changedWorkdays.value[workday.date];
  if (workdays.value[workdayIndex].status === 'processed') {
    openDays.value = openDays.value.filter((day) => day !== workdayIndex);
  }
  refreshWorkdaySummary()
}

const saveWorkdayLine = (workday) => {
  loading.value[workday.date] = true;
  let json = JSON.stringify({
    start: newLine.value.start,
    end: newLine.value.end,
    workday_id: workday.id,
  });
  api
    .post("workdayLineCreate", json)
    .then((response) => {
      updateWorkday(workday, response.data.data);
      loading.value[workday.date] = false;
      newLine.value = {};
    })
    .catch((error) => {
      loading.value[workday.date] = false;
      console.log(error);
    });
}

const saveAllWorkdays = () => {
  for (const workdayDate in changedWorkdays.value) {
    saveWorkday(workdays.value.find((workday) => workday.date === workdayDate));
  }
};

const setTravelTo = (item) => {
  const workday = workdays.value.find((workday) => workday.id === item.workday_id);
  workday.travel_to_end_line_id = (workday.travel_to_end_line_id !== item.id) ? item.id : null;
  changedWorkdays.value[workday.date] = true;
  updateCalculation(workday);
};

const setTravelFrom = (item) => {
  const workday = workdays.value.find((workday) => workday.id === item.workday_id);
  workday.travel_from_start_line_id = (workday.travel_from_start_line_id !== item.id) ? item.id : null;
  changedWorkdays.value[workday.date] = true;
  updateCalculation(workday);
};

const voidLine = (item) => {
  item.void = !item.void;

  const workday = workdays.value.find((workday) => workday.id === item.workday_id);

  changedWorkdays.value[workday.date] = true;
  updateCalculation(workday);
};

const isNonWorkingDay = (date) => {
  const isEvenWeek = getWeek(date, {weekStartsOn: 1}) % 2 === 0;
  const nonWorkingDays = isEvenWeek ? user.value.non_working_days_even_weeks ?? [] : user.value.non_working_days_uneven_weeks ?? [];

  return nonWorkingDays.indexOf(format(date, 'eeee').toLowerCase()) !== -1;
}

const getIcon = (workday, color = false) => {
  if (!workday.id) {
    return (color) ? 'grey' : 'mdi-plus';
  }

  if (changedWorkdays.value[workday.date]) {
    return (color) ? 'indufastRed' : 'mdi-content-save';
  }

  if (workday.status === 'processed') {
    return (color) ? 'indufastGreen' : 'mdi-check';
  }

  return (color) ? 'text-black' : 'mdi-alert-box-outline';
}

const getClasses = (workday) => {
  let classes = [];
  if (new Date(workday.date).getDay() === 0 || new Date(workday.date).getDay() === 6) {
    classes.push('weekend');
  }

  if (isHoliday(workday.date)) {
    classes.push('holiday-workday');
  }

  if (!workday.id) {
    classes.push('new');
  }

  return classes;
};

const rowProps = (row) => {
  return {
    class: {
      "workday-line": true,
      "void": row.item.void,
      "travel-from": !row.item.void && row.item.type === 'travel-from',
      "travel-to": !row.item.void && row.item.type === 'travel-to',
    },
  };
};

const dayHeaders = [
  {title: "Start", value: "start", cellProps: {class: "no-wrap"}},
  {title: "Eind", value: "end", cellProps: {class: "no-wrap"}},
  {title: "Duur", value: "duration", cellProps: {class: "no-wrap"}},
  {title: "Voertuig", value: "vehicle", cellProps: {class: "no-wrap"}},
  {title: "Van", value: "start_address"},
  {title: "Naar", value: "end_address"},
  {title: "Afstand", value: "distance", cellProps: {class: "no-wrap"}},
  {title: "Acties", value: "actions", cellProps: {class: "no-wrap"}},
];

const workdaySummary = computed(() => {
  return [
    {title: 'Werknemer', value: (user.value?.name !== user.value?.name_accredis ? user.value?.name + ' (Accredis: ' + user.value?.name_accredis + ')' : user.value?.name)},
    {title: 'Aantal gewerkte dagen', value: summary.value?.count},
    {title: 'Totaal gewerkte uren', value: summary.value?.total},
    {title: 'Uren per maand', value: summary.value?.hoursPerMonth},
    {title: 'Maandelijkse saldo', value: summary.value?.monthlyBalance},
    {title: 'Totaal saldo', value: summary.value?.totalBalance},
    {title: 'Uren op zaterdag', value: summary.value?.totalSaturday},
    {title: 'Uren op zondag of feestdag', value: summary.value?.totalSundayOrHoliday},
    {title: 'Overuren < 3:00', value: summary.value?.totalOvertimeBelow},
    {title: 'Overuren > 3:00', value: summary.value?.totalOvertimeAbove},
    {title: 'Verschoven uren', value: summary.value?.totalOutsideWorkingHours},
    {title: 'Reistijd', value: summary.value?.totalTravel},
  ];
});

watch(route, () => {
  refreshWorkdays();
})

const isHoliday = (date) => {
  return Object.keys(holidays.value).includes(date);
}
const getHolidayName = (date) => {
  const holiday = holidays.value[date];
  return holidayDisplayNames[holiday] ?? holiday;
}

const editDialog = ref(false);
const editingWorkday = ref(null);
const openEditDialog = (workday) => {
  editingWorkday.value = workday;
  editDialog.value = true;
};
</script>

<template>
  <v-container fluid>
    <v-row v-for="(item, index) in workdaySummary" :key="index" dense>
      <v-col cols="2">
        {{ item.title }}
      </v-col>
      <v-col>
        {{ item.value }}
      </v-col>
    </v-row>
  </v-container>

  <v-expansion-panels
    v-model="openDays"
    multiple
    elevation="0"
  >
    <v-expansion-panel
      v-for="(day, index) in workdays"
      :key="day.id"
    >
      <v-expansion-panel-title
        :class="getClasses(day)"
        :title="getWeek(day.date, {weekStartsOn: 1})"
      >
        <v-icon
          class="mr-1"
          :color="getIcon(day, true)"
        >
          {{ getIcon(day) }}
        </v-icon>
        {{ $filters.ucFirst($filters.formatDate(day.date)) }}
        <v-icon
          v-if="isNonWorkingDay(day.date)"
          icon="mdi-briefcase-variant-off-outline"
          color="indufastPurple"
          title="Deze medewerker is roostervrij op deze datum"
          class="ml-3"
        />
        <v-chip
          v-if="isHoliday(day.date)"
          color="indufastGreen"
          class="ml-3"
          variant="outlined"
        >
          {{ getHolidayName(day.date) }}
        </v-chip>
        <v-icon
          v-if="day.id && openDays.indexOf(index) !== -1"
          icon="mdi-pencil"
          class="ml-3"
          :disabled="day.status !== 'new'"
          @click.stop="openEditDialog(day)"
        />
        <v-spacer />
        <span v-if="day.status !== 'new' && openDays.indexOf(index) === -1">
          Uren gewerkt: {{ day.workdayDurationNet }} - Reistijd: {{ day.travelDurationNet }}
        </span>
      </v-expansion-panel-title>
      <v-expansion-panel-text class="pa-6">
        <v-row>
          <v-col cols="2" class="summary">
            <h4 v-if="day.workdayDuration">
              Werkdag
            </h4>
            <v-table v-if="day.workdayDuration">
              <tr v-if="!day.work_time">
                <th>Werktijd</th>
                <td>{{ day.workdayStart }} - {{ day.workdayEnd }}</td>
              </tr>
              <tr>
                <td>Bruto</td>
                <td>
                  <span :class="{'overriden': day.work_time}">
                    {{ day.workdayDuration }}
                  </span>
                </td>
              </tr>
              <tr>
                <th>Pauze</th>
                <td>
                  <span :class="{'overriden': day.break_time}">
                    {{ timeInMinutes(day.break_time || day.workdayPause) }} minuten
                  </span>
                </td>
              </tr>
              <tr class="netDuration">
                <th>Netto</th>
                <td>
                  {{ day.workdayDurationNet }}
                </td>
              </tr>
              <tr class="netDuration" v-if="day.workdayOutsideWorkingHours">
                <th>Verschoven</th>
                <td>{{ day.workdayOutsideWorkingHours }}</td>
              </tr>
              <tr class="netDuration" v-if="day.overtimeBelow">
                <th>Overuren &lt; 3:00</th>
                <td>{{ day.overtimeBelow }}</td>
              </tr>
              <tr class="netDuration" v-if="day.overtimeAbove">
                <th>Overuren &gt; 3:00</th>
                <td>{{ day.overtimeAbove }}</td>
              </tr>
            </v-table>

            <template v-if="day.travel_to_end_line_id || day.travel_from_start_line_id">
              <h4>Reistijd</h4>
              <v-table>
                <template v-if="day.travel_to_end_line_id">
                  <tr>
                    <th>Heenreis</th>
                    <td>{{ day.travelToStart }} - {{ day.travelToEnd }}</td>
                  </tr>
                  <tr>
                    <th>Bruto</th>
                    <td>{{ day.travelToDuration }}</td>
                  </tr>
                  <tr class="netDuration">
                    <th>Netto</th>
                    <td>{{ day.travelToDurationNet }}</td>
                  </tr>
                </template>

                <template v-if="day.travel_from_start_line_id">
                  <tr>
                    <th>Terugreis</th>
                    <td>{{ day.travelFromStart }} - {{ day.travelFromEnd }}</td>
                  </tr>
                  <tr>
                    <th>Bruto</th>
                    <td>{{ day.travelFromDuration }}</td>
                  </tr>
                  <tr class="netDuration">
                    <th>Netto</th>
                    <td>{{ day.travelFromDurationNet }}</td>
                  </tr>
                </template>

                <tr class="totalDuration">
                  <th>Totaal</th>
                  <td>{{ day.travelDurationNet }}</td>
                </tr>
              </v-table>
            </template>
          </v-col>
          <v-col>
            <v-data-table
              :items="day.lines"
              :headers="dayHeaders"
              item-key="id"
              hide-default-footer
              :row-props="rowProps"
              density="compact"
              :loading="loading[day.date]"
              items-per-page="-1"
            >
              <template #item.distance="{ value }">
                {{ value ? $filters.formatDistance(value) : '' }}
              </template>

              <template #item.actions="{ item }">
                <v-icon
                  :disabled="!item.canBeSetAsTravelToEnd || day.status !== 'new'"
                  color="indufastGreen"
                  title="Heenreis instellen"
                  @click="setTravelTo(item)"
                >
                  mdi-arrow-collapse-down
                </v-icon>
                <v-icon
                  :disabled="!item.canBeSetAsTravelFromStart || day.status !== 'new'"
                  color="indufastCyan"
                  title="Terugreis instellen"
                  @click="setTravelFrom(item)"
                >
                  mdi-arrow-collapse-up
                </v-icon>
                <v-icon
                  :disabled="!item.canBeVoid || day.status !== 'new'"
                  color="indufastRed"
                  :title="item.void ? 'Regel inschakelen' : 'Regel uitschakelen'"
                  @click="voidLine(item)"
                >
                  {{ item.void ? 'mdi-undo' : 'mdi-close' }}
                </v-icon>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-textarea
              v-model="day.remark"
              label="Opmerkingen"
              variant="outlined"
              :disabled="day.status !== 'new'"
              rows="1"
              auto-grow
              hide-details
              @keydown="changedWorkdays[day.date] = true"
            />
          </v-col>
        </v-row>
        <v-row class="mt-0">
          <v-col>
            <v-card-actions>
              <v-spacer />
              <v-dialog max-width="500">
                <template #activator="{ props: activatorProps }">
                  <v-btn
                    v-bind="activatorProps"
                    prepend-icon="mdi-plus"
                    :disabled="day.status !== 'new' || !day.id"
                  >
                    Regel toevoegen
                  </v-btn>
                </template>
                <template #default="{ isActive }">
                  <v-card>
                    <v-card-title class="bg-primary">
                      Regel toevoegen op {{ day.date }}
                    </v-card-title>
                    <v-card-text>
                      <v-text-field
                        v-model="newLine.start"
                        v-maska="'##:##'"
                        placeholder="07:00"
                        label="Van"
                        prepend-icon="mdi-clock-time-four-outline"
                      />
                      <v-text-field
                        v-model="newLine.end"
                        v-maska="'##:##'"
                        placeholder="17:00"
                        label="Tot"
                        prepend-icon="mdi-clock-time-four-outline"
                      />
                    </v-card-text>
                    <v-card-actions>
                      <v-btn
                        text="Annuleren"
                        @click="isActive.value = false"
                      />
                      <v-btn
                        text="Opslaan"
                        color="primary"
                        variant="elevated"
                        @click="saveWorkdayLine(day); isActive.value = false"
                      />
                    </v-card-actions>
                  </v-card>
                </template>
              </v-dialog>
              <v-checkbox
                v-model="day.status"
                label="Afgerond"
                density="compact"
                false-value="new"
                true-value="processed"
                class="mr-2"
                color="primary"
                hide-details
                :disabled="!day.id"
                @change="changedWorkdays[day.date] = true"
              />
              <v-btn
                color="primary"
                variant="elevated"
                :prepend-icon="day.id ? 'mdi-content-save' : 'mdi-plus'"
                :disabled="day.id && !changedWorkdays[day.date]"
                :loading="loading[day.date]"
                @click="saveWorkday(day)"
              >
                {{ day.id ? 'Opslaan' : 'Werkdag aanmaken' }}
              </v-btn>
            </v-card-actions>
          </v-col>
        </v-row>
      </v-expansion-panel-text>
    </v-expansion-panel>
  </v-expansion-panels>
  <v-fab
    v-if="Object.keys(changedWorkdays).length"
    size="large"
    color="primary"
    location="top right"
    prepend-icon="mdi-content-save"
    app
    @click="saveAllWorkdays"
  >
    Sla alles op ({{ Object.keys(changedWorkdays).length }})
  </v-fab>

  <workday-edit-dialog
    v-model="editDialog"
    :workday="editingWorkday"
    @saved="refreshWorkdays"
  />
</template>

<style lang="scss">
tr.workday-line td:first-child {
  border-left: 10px solid lightgray;
}

tr.void td:first-child {
  border-left: 10px solid rgba(var(--v-theme-indufastRed), .75);
}

tr.void {
  background-color: rgba(var(--v-theme-indufastRed), .1);
}

tr.travel-to td:first-child {
  border-left: 10px solid rgba(var(--v-theme-indufastGreen), .75);
}

tr.travel-to {
  background-color: rgba(var(--v-theme-indufastGreen), .1);
}

tr.travel-from td:first-child {
  border-left: 10px solid rgba(var(--v-theme-indufastCyan), .75);
}

tr.travel-from {
  background-color: rgba(var(--v-theme-indufastCyan), .1);
}

.v-icon--disabled {
  color: grey !important;
}

.workday-title {
  background-color: rgba(var(--v-theme-indufastCyan), .1);
}

td.no-wrap {
  white-space: nowrap;
  width: 0;
}
tr.netDuration,
tr.totalDuration {
  font-weight: bold;
}

.summary {
  min-width: 280px;
}

.summary td {
  white-space: nowrap;
}

.summary th {
  font-weight: normal;
  text-align: left;
  width: 120px;
}
.summary .totalDuration th {
  font-weight: bold;
}
.summary tr.totalDuration th,
.summary tr.totalDuration td,
.summary tr.netDuration th,
.summary tr.netDuration td {
  padding-bottom: 10px !important;
}

.weekend {
  background-color: rgba(var(--v-theme-indufastYellow), var(--v-extra-light-opacity));
}

.holiday-workday {
  background-color: rgba(var(--v-theme-indufastGreen), var(--v-extra-light-opacity));
}

.new .v-expansion-panel-title:not(.v-expansion-panel-title--active) {
  color: grey;
}
.overriden {
  background-color: rgba(var(--v-theme-indufastRed), var(--v-extra-light-opacity));
  border-radius: 5px;
  padding-left: 5px;
  padding-right: 5px;
}
</style>
